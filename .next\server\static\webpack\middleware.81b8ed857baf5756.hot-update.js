"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb)\n/* harmony export */ });\n// 开发环境模拟数据库\nconst mockDatabase = {\n    users: new Map(),\n    emails: new Map(),\n    messages: new Map(),\n    emailCredentials: new Map(),\n    roles: new Map(),\n    userRoles: new Map(),\n    apiKeys: new Map(),\n    accounts: new Map(),\n    sessions: new Map(),\n    verificationTokens: new Map()\n};\n// 初始化一些示例数据\nfunction initMockData() {\n    // 创建示例用户\n    const user = {\n        id: 'user-1',\n        name: 'Demo User',\n        email: '<EMAIL>',\n        emailVerified: null,\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    mockDatabase.users.set(user.id, user);\n    // 创建示例邮箱\n    const email = {\n        id: 'email-1',\n        address: '<EMAIL>',\n        userId: user.id,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n    };\n    mockDatabase.emails.set(email.id, email);\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});