import { NextResponse } from "next/server"
import { createDb } from "@/lib/db"
import { messages } from "@/lib/schema"
import { eq, and } from "drizzle-orm"
import { validateCredential } from "@/lib/emailCredentials"

export const runtime = "edge"

/**
 * 通过凭证获取单个邮件详情
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ credential: string; messageId: string }> }
) {
  try {
    const { credential, messageId } = await params
    
    if (!credential || !messageId) {
      return NextResponse.json(
        { error: "凭证和邮件ID不能为空" },
        { status: 400 }
      )
    }
    
    // 验证凭证
    const credentialInfo = await validateCredential(credential)
    
    if (!credentialInfo) {
      return NextResponse.json(
        { error: "凭证无效或已过期" },
        { status: 404 }
      )
    }
    
    const db = createDb()
    
    // 查询邮件
    const message = await db.query.messages.findFirst({
      where: and(
        eq(messages.id, messageId),
        eq(messages.emailId, credentialInfo.email.id)
      )
    })
    
    if (!message) {
      return NextResponse.json(
        { error: "邮件不存在" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ 
      message: {
        id: message.id,
        from_address: message.fromAddress,
        subject: message.subject,
        content: message.content,
        html: message.html,
        received_at: message.receivedAt.getTime()
      },
      email: {
        id: credentialInfo.email.id,
        address: credentialInfo.email.address
      }
    })
  } catch (error) {
    console.error('Failed to fetch message by credential:', error)
    return NextResponse.json(
      { error: "获取邮件失败" },
      { status: 500 }
    )
  }
}
