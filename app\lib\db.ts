import { getRequestContext } from "@cloudflare/next-on-pages"
import { drizzle } from "drizzle-orm/d1"
import * as schema from "./schema"

export const createDb = () => {
  try {
    // 尝试获取 Cloudflare 环境
    const context = getRequestContext()
    return drizzle(context.env.DB, { schema })
  } catch (error) {
    // 开发环境回退：创建一个模拟的数据库连接
    console.warn('Cloudflare context not available, using development mode')

    // 创建一个简单的内存数据库模拟
    const mockDb = {
      query: {
        emails: {
          findFirst: () => Promise.resolve(null),
          findMany: () => Promise.resolve([])
        },
        emailCredentials: {
          findFirst: () => Promise.resolve(null),
          findMany: () => Promise.resolve([])
        },
        messages: {
          findFirst: () => Promise.resolve(null),
          findMany: () => Promise.resolve([])
        },
        users: {
          findFirst: () => Promise.resolve(null),
          findMany: () => Promise.resolve([])
        }
      },
      insert: () => ({
        values: () => Promise.resolve({ success: true })
      }),
      update: () => ({
        set: () => ({
          where: () => Promise.resolve({ success: true })
        })
      }),
      delete: () => ({
        where: () => Promise.resolve({ success: true, changes: 0 })
      })
    }

    return mockDb as any
  }
}

export type Db = ReturnType<typeof createDb>
