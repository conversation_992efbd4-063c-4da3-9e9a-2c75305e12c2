import { createDb, mockDatabase } from "@/lib/db"
import { and, eq, gt, lt, or, sql } from "drizzle-orm"
import { NextResponse } from "next/server"
import { emails } from "@/lib/schema"
import { encodeCursor, decodeCursor } from "@/lib/cursor"
import { getUserId } from "@/lib/apiKey"

export const runtime = "edge"

const PAGE_SIZE = 20

export async function GET(request: Request) {
  const userId = await getUserId()

  const { searchParams } = new URL(request.url)
  const cursor = searchParams.get('cursor')

  const db = createDb()

  try {
    // 开发环境演示数据 + 动态创建的邮箱
    const allEmails = Array.from(mockDatabase.emails.values())

    console.log('🔍 获取邮箱列表 - 用户ID:', userId)
    console.log('🔍 数据库中所有邮箱:', allEmails.length)

    // 固定的演示邮箱
    const demoEmails = [
      {
        id: 'demo-email-1',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1小时前
        expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000) // 23小时后
      },
      {
        id: 'demo-email-2',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
        expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000) // 23.5小时后
      },
      {
        id: 'demo-email-3',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10分钟前
        expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000) // 23.8小时后
      }
    ]

    // 合并演示邮箱和动态创建的邮箱
    const combinedEmails = [...demoEmails, ...allEmails]

    const userEmails = combinedEmails
      .filter(email => email.userId === userId && new Date(email.expiresAt) > new Date())
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    console.log('🔍 用户邮箱:', userEmails)
    const totalCount = userEmails.length

    // 简化分页逻辑
    let startIndex = 0
    if (cursor) {
      try {
        const { timestamp, id } = decodeCursor(cursor)
        startIndex = userEmails.findIndex(email =>
          new Date(email.createdAt).getTime() < timestamp ||
          (new Date(email.createdAt).getTime() === timestamp && email.id < id)
        )
        if (startIndex === -1) startIndex = userEmails.length
      } catch (e) {
        startIndex = 0
      }
    }

    const emailList = userEmails.slice(startIndex, startIndex + PAGE_SIZE)
    const hasMore = startIndex + PAGE_SIZE < userEmails.length
    const nextCursor = hasMore && emailList.length > 0
      ? encodeCursor(
          new Date(emailList[emailList.length - 1].createdAt).getTime(),
          emailList[emailList.length - 1].id
        )
      : null

    return NextResponse.json({
      emails: emailList,
      nextCursor,
      total: totalCount
    })
  } catch (error) {
    console.error('Failed to fetch user emails:', error)
    return NextResponse.json(
      { error: "Failed to fetch emails" },
      { status: 500 }
    )
  }
}