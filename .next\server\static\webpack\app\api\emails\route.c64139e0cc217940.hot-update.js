"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/api/emails/route",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 使用全局变量确保数据在热重载时保持\nif (!__webpack_require__.g.__mockDatabase) {\n    __webpack_require__.g.__mockDatabase = {\n        users: new Map(),\n        emails: new Map(),\n        messages: new Map(),\n        emailCredentials: new Map(),\n        roles: new Map(),\n        userRoles: new Map(),\n        apiKeys: new Map(),\n        accounts: new Map(),\n        sessions: new Map(),\n        verificationTokens: new Map(),\n        webhooks: new Map()\n    };\n}\n// 导出全局数据库实例\nconst mockDatabase = __webpack_require__.g.__mockDatabase;\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});