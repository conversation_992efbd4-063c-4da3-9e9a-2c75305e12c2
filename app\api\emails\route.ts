import { createDb, mockDatabase } from "@/lib/db"
import { and, eq, gt, lt, or, sql } from "drizzle-orm"
import { NextResponse } from "next/server"
import { emails } from "@/lib/schema"
import { encodeCursor, decodeCursor } from "@/lib/cursor"
import { getUserId } from "@/lib/apiKey"

export const runtime = "edge"

const PAGE_SIZE = 20

export async function GET(request: Request) {
  const userId = await getUserId()

  const { searchParams } = new URL(request.url)
  const cursor = searchParams.get('cursor')

  const db = createDb()

  try {
    // 从数据库获取邮箱
    const userEmails = await db.query.emails.findMany({
      where: and(
        eq(emails.userId, userId!),
        gt(emails.expiresAt, new Date())
      ),
      orderBy: (emails, { desc }) => [desc(emails.createdAt)]
    })

    console.log('🔍 获取邮箱列表 - 用户ID:', userId)
    console.log('🔍 用户邮箱数量:', userEmails.length)

    const totalCount = userEmails.length

    // 简化分页逻辑
    let startIndex = 0
    if (cursor) {
      try {
        const { timestamp, id } = decodeCursor(cursor)
        startIndex = userEmails.findIndex(email =>
          new Date(email.createdAt).getTime() < timestamp ||
          (new Date(email.createdAt).getTime() === timestamp && email.id < id)
        )
        if (startIndex === -1) startIndex = userEmails.length
      } catch (e) {
        startIndex = 0
      }
    }

    const emailList = userEmails.slice(startIndex, startIndex + PAGE_SIZE)
    const hasMore = startIndex + PAGE_SIZE < userEmails.length
    const nextCursor = hasMore && emailList.length > 0
      ? encodeCursor(
          new Date(emailList[emailList.length - 1].createdAt).getTime(),
          emailList[emailList.length - 1].id
        )
      : null

    return NextResponse.json({
      emails: emailList,
      nextCursor,
      total: totalCount
    })
  } catch (error) {
    console.error('Failed to fetch user emails:', error)
    return NextResponse.json(
      { error: "Failed to fetch emails" },
      { status: 500 }
    )
  }
}