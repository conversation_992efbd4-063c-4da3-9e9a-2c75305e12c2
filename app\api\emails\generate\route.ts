import { NextResponse } from "next/server"
import { nanoid } from "nanoid"
import { createDb, mockDatabase, saveData } from "@/lib/db"
import { emails } from "@/lib/schema"
import { eq, and, gt, sql } from "drizzle-orm"
import { EXPIRY_OPTIONS } from "@/types/email"
import { EMAIL_CONFIG } from "@/config"
import { getUserId } from "@/lib/apiKey"
import { getUserRole } from "@/lib/auth"
import { ROLES } from "@/lib/permissions"

export const runtime = "edge"

export async function POST(request: Request) {
  const db = createDb()

  const userId = await getUserId()
  const userRole = await getUserRole(userId!)

  try {
    // 开发环境简化邮箱数量检查
    if (userRole !== ROLES.EMPEROR) {
      const maxEmails = EMAIL_CONFIG.MAX_ACTIVE_EMAILS
      const userEmails = Array.from(mockDatabase.emails.values())
        .filter(email => email.userId === userId && new Date(email.expiresAt) > new Date())

      if (userEmails.length >= maxEmails) {
        return NextResponse.json(
          { error: `已达到最大邮箱数量限制 (${maxEmails})` },
          { status: 403 }
        )
      }
    }

    const { name, expiryTime, domain } = await request.json<{ 
      name: string
      expiryTime: number
      domain: string
    }>()

    if (!EXPIRY_OPTIONS.some(option => option.value === expiryTime)) {
      return NextResponse.json(
        { error: "无效的过期时间" },
        { status: 400 }
      )
    }

    // 开发环境使用默认域名
    const domains = ["moemail.app", "temp.email", "demo.mail"]

    if (!domains || !domains.includes(domain)) {
      return NextResponse.json(
        { error: "无效的域名" },
        { status: 400 }
      )
    }

    const address = `${name || nanoid(8)}@${domain}`

    // 开发环境简化重复检查
    const existingEmail = Array.from(mockDatabase.emails.values())
      .find(email => email.address.toLowerCase() === address.toLowerCase())

    if (existingEmail) {
      return NextResponse.json(
        { error: "该邮箱地址已被使用" },
        { status: 409 }
      )
    }

    const now = new Date()
    const expires = expiryTime === 0 
      ? new Date('9999-01-01T00:00:00.000Z')
      : new Date(now.getTime() + expiryTime)
    
    const emailData: typeof emails.$inferInsert = {
      address,
      createdAt: now,
      expiresAt: expires,
      userId: userId!
    }
    
    // 开发环境直接插入到模拟数据库
    const emailId = `email-${Date.now()}-${nanoid(6)}`
    const newEmail = {
      id: emailId,
      address,
      userId: userId!,
      createdAt: now,
      expiresAt: expires
    }

    mockDatabase.emails.set(emailId, newEmail)
    saveData() // 保存到持久化存储

    console.log('📧 邮箱已创建:', newEmail)
    console.log('📊 当前邮箱总数:', mockDatabase.emails.size)
    console.log('📋 所有邮箱:', Array.from(mockDatabase.emails.values()))

    return NextResponse.json({
      id: emailId,
      email: address
    })
  } catch (error) {
    console.error('Failed to generate email:', error)
    return NextResponse.json(
      { error: "创建邮箱失败" },
      { status: 500 }
    )
  }
} 