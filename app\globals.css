@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 255 65% 52%;  /* #826DD9 */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 255 65% 52%;  /* #826DD9 */
    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 255 65% 52%;  /* #826DD9 */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 255 65% 52%;  /* #826DD9 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-grid-primary {
    background-image: linear-gradient(var(--primary) 1px, transparent 1px),
      linear-gradient(90deg, var(--primary) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* 整体滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    @apply bg-primary/20 rounded-full hover:bg-primary/40 transition-colors;
  }

  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.2) transparent;
  }

  /* 深色模式下的滚动条 */
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 hover:bg-primary/50;
  }

  .dark * {
    scrollbar-color: hsl(var(--primary) / 0.3) transparent;
  }
}
