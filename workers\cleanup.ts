interface Env {
  DB: D1Database
}

const CLEANUP_CONFIG = {
  // Whether to delete expired emails
  DELETE_EXPIRED_EMAILS: true,

  // Whether to delete expired credentials
  DELETE_EXPIRED_CREDENTIALS: true,

  // Batch processing size
  BATCH_SIZE: 100,
} as const

const main = {
  async scheduled(_: ScheduledEvent, env: Env) {
    const now = Date.now()

    try {
      if (!CLEANUP_CONFIG.DELETE_EXPIRED_EMAILS) {
        console.log('Expired email deletion is disabled')
        return
      }

      const result = await env.DB
        .prepare(`
          DELETE FROM email 
          WHERE expires_at < ?
          LIMIT ?
        `)
        .bind(now, CLEANUP_CONFIG.BATCH_SIZE)
        .run()

      if (result.success) {
        console.log(`Deleted ${result?.meta?.changes ?? 0} expired emails and their associated messages`)
      } else {
        console.error('Failed to delete expired emails')
      }

      // 清理过期凭证
      if (CLEANUP_CONFIG.DELETE_EXPIRED_CREDENTIALS) {
        const credentialResult = await env.<PERSON>
          .prepare(`
            DELETE FROM email_credentials
            WHERE expires_at < ?
            LIMIT ?
          `)
          .bind(now, CLEANUP_CONFIG.BATCH_SIZE)
          .run()

        if (credentialResult.success) {
          console.log(`Deleted ${credentialResult?.meta?.changes ?? 0} expired credentials`)
        } else {
          console.error('Failed to delete expired credentials')
        }
      }
    } catch (error) {
      console.error('Failed to cleanup:', error)
      throw error
    }
  }
}

export default main
