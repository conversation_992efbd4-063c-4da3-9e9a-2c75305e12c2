{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}}, "functions": {"/api/auth/[...auth]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/auth/[...auth]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/auth/[...auth]/route.js"], "name": "app/api/auth/[...auth]/route", "page": "/api/auth/[...auth]/route", "matchers": [{"regexp": "^/api/auth/(?<auth>.+?)$", "originalSource": "/api/auth/[...auth]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/api/emails/route": {"files": ["server/server-reference-manifest.js", "server/app/api/emails/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/emails/route.js"], "name": "app/api/emails/route", "page": "/api/emails/route", "matchers": [{"regexp": "^/api/emails$", "originalSource": "/api/emails"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/api/emails/generate/route": {"files": ["server/server-reference-manifest.js", "server/app/api/emails/generate/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/emails/generate/route.js"], "name": "app/api/emails/generate/route", "page": "/api/emails/generate/route", "matchers": [{"regexp": "^/api/emails/generate$", "originalSource": "/api/emails/generate"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/page": {"files": ["server/server-reference-manifest.js", "server/app/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/page.js"], "name": "app/page", "page": "/page", "matchers": [{"regexp": "^/$", "originalSource": "/"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/moe/page": {"files": ["server/server-reference-manifest.js", "server/app/moe/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/moe/page.js"], "name": "app/moe/page", "page": "/moe/page", "matchers": [{"regexp": "^/moe$", "originalSource": "/moe"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/profile/page": {"files": ["server/server-reference-manifest.js", "server/app/profile/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/profile/page.js"], "name": "app/profile/page", "page": "/profile/page", "matchers": [{"regexp": "^/profile$", "originalSource": "/profile"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}}, "sortedMiddleware": ["/"]}