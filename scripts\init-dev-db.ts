import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import { migrate } from 'drizzle-orm/better-sqlite3/migrator'
import path from 'path'
import fs from 'fs'

// 创建开发数据库
async function initDevDatabase() {
  console.log('🚀 初始化开发数据库...')
  
  const dbPath = path.join(process.cwd(), 'dev.db')
  
  // 如果数据库文件已存在，删除它
  if (fs.existsSync(dbPath)) {
    fs.unlinkSync(dbPath)
    console.log('🗑️ 删除旧的数据库文件')
  }
  
  // 创建新的数据库连接
  const sqlite = new Database(dbPath)
  sqlite.pragma('foreign_keys = ON')
  
  const db = drizzle(sqlite)
  
  // 手动创建表结构（因为 drizzle 迁移可能有问题）
  console.log('📋 创建表结构...')
  
  // 用户表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "user" (
      "id" text PRIMARY KEY NOT NULL,
      "name" text,
      "email" text,
      "emailVerified" integer,
      "image" text,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL,
      "updated_at" integer DEFAULT (unixepoch()) NOT NULL
    );
  `)
  
  // 账户表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "account" (
      "userId" text NOT NULL,
      "type" text NOT NULL,
      "provider" text NOT NULL,
      "providerAccountId" text NOT NULL,
      "refresh_token" text,
      "access_token" text,
      "expires_at" integer,
      "token_type" text,
      "scope" text,
      "id_token" text,
      "session_state" text,
      PRIMARY KEY("provider", "providerAccountId"),
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // 会话表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "session" (
      "sessionToken" text PRIMARY KEY NOT NULL,
      "userId" text NOT NULL,
      "expires" integer NOT NULL,
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // 验证令牌表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "verificationToken" (
      "identifier" text NOT NULL,
      "token" text NOT NULL,
      "expires" integer NOT NULL,
      PRIMARY KEY("identifier", "token")
    );
  `)
  
  // 角色表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "role" (
      "id" text PRIMARY KEY NOT NULL,
      "name" text NOT NULL UNIQUE,
      "description" text,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL
    );
  `)
  
  // 用户角色表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "user_role" (
      "id" text PRIMARY KEY NOT NULL,
      "user_id" text NOT NULL,
      "role_id" text NOT NULL,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL,
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON UPDATE no action ON DELETE cascade,
      FOREIGN KEY ("role_id") REFERENCES "role"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // 邮箱表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "email" (
      "id" text PRIMARY KEY NOT NULL,
      "address" text NOT NULL UNIQUE,
      "user_id" text,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL,
      "expires_at" integer NOT NULL,
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON UPDATE no action ON DELETE set null
    );
  `)
  
  // 邮件表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "message" (
      "id" text PRIMARY KEY NOT NULL,
      "email_id" text NOT NULL,
      "from_address" text NOT NULL,
      "subject" text NOT NULL,
      "content" text NOT NULL,
      "html" text,
      "received_at" integer DEFAULT (unixepoch()) NOT NULL,
      FOREIGN KEY ("email_id") REFERENCES "email"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // API密钥表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "api_keys" (
      "id" text PRIMARY KEY NOT NULL,
      "user_id" text NOT NULL,
      "name" text NOT NULL,
      "key" text NOT NULL UNIQUE,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL,
      "expires_at" integer,
      "enabled" integer DEFAULT 1 NOT NULL,
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // 邮箱凭证表
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS "email_credentials" (
      "id" text PRIMARY KEY NOT NULL,
      "email_id" text NOT NULL,
      "credential" text NOT NULL UNIQUE,
      "created_at" integer DEFAULT (unixepoch()) NOT NULL,
      "expires_at" integer NOT NULL,
      "last_accessed_at" integer,
      "access_count" integer DEFAULT 0 NOT NULL,
      "enabled" integer DEFAULT 1 NOT NULL,
      FOREIGN KEY ("email_id") REFERENCES "email"("id") ON UPDATE no action ON DELETE cascade
    );
  `)
  
  // 创建索引
  console.log('🔍 创建索引...')
  
  sqlite.exec(`
    CREATE INDEX IF NOT EXISTS "email_credentials_credential_idx" ON "email_credentials" ("credential");
    CREATE INDEX IF NOT EXISTS "email_credentials_email_id_idx" ON "email_credentials" ("email_id");
    CREATE INDEX IF NOT EXISTS "email_credentials_expires_at_idx" ON "email_credentials" ("expires_at");
    CREATE UNIQUE INDEX IF NOT EXISTS "name_user_id_unique" ON "api_keys" ("name", "user_id");
  `)
  
  // 插入默认角色
  console.log('👑 插入默认角色...')
  
  sqlite.exec(`
    INSERT OR IGNORE INTO "role" ("id", "name", "description") VALUES 
    ('emperor', '皇帝', '最高权限'),
    ('duke', '公爵', '高级权限'),
    ('knight', '骑士', '中级权限'),
    ('civilian', '平民', '基础权限');
  `)
  
  sqlite.close()
  
  console.log('✅ 开发数据库初始化完成！')
  console.log(`📍 数据库文件位置: ${dbPath}`)
}

// 运行初始化
if (require.main === module) {
  initDevDatabase().catch(console.error)
}

export { initDevDatabase }
