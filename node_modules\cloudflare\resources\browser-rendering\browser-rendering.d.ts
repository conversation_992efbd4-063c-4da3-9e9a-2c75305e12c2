import { APIResource } from "../../resource.js";
import * as ContentAPI from "./content.js";
import { Content, ContentCreateParams, ContentCreateResponse } from "./content.js";
import * as JsonAP<PERSON> from "./json.js";
import { Json, JsonCreateParams, JsonCreateResponse } from "./json.js";
import * as LinksAP<PERSON> from "./links.js";
import { LinkCreateParams, LinkCreateResponse, Links } from "./links.js";
import * as MarkdownAPI from "./markdown.js";
import { Markdown, MarkdownCreateParams, MarkdownCreateResponse } from "./markdown.js";
import * as PDFAPI from "./pdf.js";
import { PDF, PDFCreateParams } from "./pdf.js";
import * as ScrapeAPI from "./scrape.js";
import { Scrape, ScrapeCreateParams, ScrapeCreateResponse } from "./scrape.js";
import * as ScreenshotAPI from "./screenshot.js";
import { Screenshot, ScreenshotCreateParams, ScreenshotCreateResponse } from "./screenshot.js";
import * as Snap<PERSON>AP<PERSON> from "./snapshot.js";
import { Snapshot, SnapshotCreateParams, SnapshotCreateResponse } from "./snapshot.js";
export declare class BrowserRendering extends APIResource {
    content: ContentAPI.Content;
    pdf: PDFAPI.PDF;
    scrape: ScrapeAPI.Scrape;
    screenshot: ScreenshotAPI.Screenshot;
    snapshot: SnapshotAPI.Snapshot;
    json: JsonAPI.Json;
    links: LinksAPI.Links;
    markdown: MarkdownAPI.Markdown;
}
export declare namespace BrowserRendering {
    export { Content as Content, type ContentCreateResponse as ContentCreateResponse, type ContentCreateParams as ContentCreateParams, };
    export { PDF as PDF, type PDFCreateParams as PDFCreateParams };
    export { Scrape as Scrape, type ScrapeCreateResponse as ScrapeCreateResponse, type ScrapeCreateParams as ScrapeCreateParams, };
    export { Screenshot as Screenshot, type ScreenshotCreateResponse as ScreenshotCreateResponse, type ScreenshotCreateParams as ScreenshotCreateParams, };
    export { Snapshot as Snapshot, type SnapshotCreateResponse as SnapshotCreateResponse, type SnapshotCreateParams as SnapshotCreateParams, };
    export { Json as Json, type JsonCreateResponse as JsonCreateResponse, type JsonCreateParams as JsonCreateParams, };
    export { Links as Links, type LinkCreateResponse as LinkCreateResponse, type LinkCreateParams as LinkCreateParams, };
    export { Markdown as Markdown, type MarkdownCreateResponse as MarkdownCreateResponse, type MarkdownCreateParams as MarkdownCreateParams, };
}
//# sourceMappingURL=browser-rendering.d.ts.map