import type { NextConfig } from "next";
import { setupDevPlatform } from '@cloudflare/next-on-pages/next-dev';

// 设置开发环境
async function setup() {
  if (process.env.NODE_ENV === 'development') {
    await setupDevPlatform();
  }
}

setup();

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
      },
    ],
  },
  serverExternalPackages: ['@auth/drizzle-adapter'],
};

export default nextConfig;
