import { NextResponse } from "next/server"
import { createDb } from "@/lib/db"
import { messages } from "@/lib/schema"
import { eq, and, lt, or, sql } from "drizzle-orm"
import { validateCredential } from "@/lib/emailCredentials"
import { encodeCursor, decodeCursor } from "@/lib/cursor"

export const runtime = "edge"

const PAGE_SIZE = 20

/**
 * 通过凭证获取邮箱的邮件列表
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ credential: string }> }
) {
  try {
    const { credential } = await params
    const url = new URL(request.url)
    const cursorStr = url.searchParams.get('cursor')
    
    if (!credential) {
      return NextResponse.json(
        { error: "凭证不能为空" },
        { status: 400 }
      )
    }
    
    // 验证凭证
    const credentialInfo = await validateCredential(credential)
    
    if (!credentialInfo) {
      return NextResponse.json(
        { error: "凭证无效或已过期" },
        { status: 404 }
      )
    }
    
    const db = createDb()
    const emailId = credentialInfo.email.id
    
    // 获取总数
    const totalResult = await db.select({ count: sql<number>`count(*)` })
      .from(messages)
      .where(eq(messages.emailId, emailId))
    const totalCount = Number(totalResult[0].count)
    
    // 构建查询条件
    const baseConditions = eq(messages.emailId, emailId)
    const conditions = [baseConditions]
    
    if (cursorStr) {
      const { timestamp, id } = decodeCursor(cursorStr)
      conditions.push(
        // @ts-expect-error "ignore the error"
        or(
          lt(messages.receivedAt, new Date(timestamp)),
          and(
            eq(messages.receivedAt, new Date(timestamp)),
            lt(messages.id, id)
          )
        )
      )
    }
    
    // 查询邮件
    const results = await db.query.messages.findMany({
      where: and(...conditions),
      orderBy: (messages, { desc }) => [
        desc(messages.receivedAt),
        desc(messages.id)
      ],
      limit: PAGE_SIZE + 1
    })
    
    const hasMore = results.length > PAGE_SIZE
    const nextCursor = hasMore 
      ? encodeCursor(
          results[PAGE_SIZE - 1].receivedAt.getTime(),
          results[PAGE_SIZE - 1].id
        )
      : null
    const messageList = hasMore ? results.slice(0, PAGE_SIZE) : results
    
    return NextResponse.json({ 
      messages: messageList.map(msg => ({
        id: msg.id,
        from_address: msg.fromAddress,
        subject: msg.subject,
        received_at: msg.receivedAt.getTime()
      })),
      nextCursor,
      total: totalCount,
      email: {
        id: credentialInfo.email.id,
        address: credentialInfo.email.address
      }
    })
  } catch (error) {
    console.error('Failed to fetch messages by credential:', error)
    return NextResponse.json(
      { error: "获取邮件失败" },
      { status: 500 }
    )
  }
}
