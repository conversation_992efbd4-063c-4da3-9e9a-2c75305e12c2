import * as schema from "./schema"

// 使用 process 对象存储数据，确保在整个进程生命周期中持久
if (!process.env.MOCK_DATABASE_DATA) {
  process.env.MOCK_DATABASE_DATA = JSON.stringify({
    emails: {},
    roles: {},
    emailCredentials: {}
  })
}

// 内存数据库
const memoryDatabase = {
  users: new Map(),
  emails: new Map(),
  messages: new Map(),
  emailCredentials: new Map(),
  roles: new Map(),
  userRoles: new Map(),
  apiKeys: new Map(),
  accounts: new Map(),
  sessions: new Map(),
  verificationTokens: new Map(),
  webhooks: new Map()
}

// 从 process.env 恢复数据
function loadData() {
  try {
    const data = JSON.parse(process.env.MOCK_DATABASE_DATA || '{}')

    // 恢复邮箱数据
    Object.entries(data.emails || {}).forEach(([key, email]: [string, any]) => {
      memoryDatabase.emails.set(key, {
        ...email,
        createdAt: new Date(email.createdAt),
        expiresAt: new Date(email.expiresAt)
      })
    })

    // 恢复角色数据
    Object.entries(data.roles || {}).forEach(([key, role]: [string, any]) => {
      memoryDatabase.roles.set(key, {
        ...role,
        createdAt: new Date(role.createdAt)
      })
    })

    // 恢复凭证数据
    Object.entries(data.emailCredentials || {}).forEach(([key, cred]: [string, any]) => {
      memoryDatabase.emailCredentials.set(key, {
        ...cred,
        createdAt: new Date(cred.createdAt),
        expiresAt: new Date(cred.expiresAt),
        lastAccessedAt: cred.lastAccessedAt ? new Date(cred.lastAccessedAt) : null
      })
    })

    console.log('📂 数据恢复完成:', {
      emails: memoryDatabase.emails.size,
      roles: memoryDatabase.roles.size,
      credentials: memoryDatabase.emailCredentials.size
    })
  } catch (e) {
    console.log('恢复数据失败:', e)
  }
}

// 保存数据到 process.env
function saveData() {
  try {
    const data = {
      emails: Object.fromEntries(memoryDatabase.emails),
      roles: Object.fromEntries(memoryDatabase.roles),
      emailCredentials: Object.fromEntries(memoryDatabase.emailCredentials)
    }
    process.env.MOCK_DATABASE_DATA = JSON.stringify(data)
    console.log('💾 数据保存完成')
  } catch (e) {
    console.log('保存数据失败:', e)
  }
}

// 导出数据库实例和保存函数
export const mockDatabase = memoryDatabase
export { saveData }

// 初始化一些示例数据
function initMockData() {
  // 先加载数据
  loadData()

  // 检查是否已有角色数据
  if (mockDatabase.roles.size > 0) return

  // 创建示例角色
  const roles = [
    { id: 'emperor', name: '皇帝', description: '最高权限', createdAt: new Date() },
    { id: 'duke', name: '公爵', description: '高级权限', createdAt: new Date() },
    { id: 'knight', name: '骑士', description: '中级权限', createdAt: new Date() },
    { id: 'civilian', name: '平民', description: '基础权限', createdAt: new Date() }
  ]
  roles.forEach(role => mockDatabase.roles.set(role.id, role))

  saveData()
  console.log('✅ 模拟数据库初始化完成')
}

// 初始化数据
initMockData()

export const createDb = () => {
  const mockDb = {
    query: {
      users: {
        findFirst: (options?: any) => {
          const users = Array.from(mockDatabase.users.values())
          return Promise.resolve(users[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.users.values()))
      },
      emails: {
        findFirst: (options?: any) => {
          const emails = Array.from(mockDatabase.emails.values())
          return Promise.resolve(emails[0] || null)
        },
        findMany: (options?: any) => {
          const emails = Array.from(mockDatabase.emails.values())
          // 简单的过滤和排序
          return Promise.resolve(emails.sort((a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          ))
        }
      },
      emailCredentials: {
        findFirst: (options?: any) => {
          const credentials = Array.from(mockDatabase.emailCredentials.values())
          return Promise.resolve(credentials[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))
      },
      messages: {
        findFirst: (options?: any) => {
          const messages = Array.from(mockDatabase.messages.values())
          return Promise.resolve(messages[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.messages.values()))
      },
      userRoles: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      roles: {
        findFirst: (options?: any) => {
          const roles = Array.from(mockDatabase.roles.values())
          return Promise.resolve(roles[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.roles.values()))
      },
      apiKeys: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      accounts: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      sessions: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      verificationTokens: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      webhooks: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      }
    },
    select: (fields: any) => ({
      from: (table: any) => ({
        where: (condition: any) => {
          // 模拟 count 查询
          if (fields.count) {
            return Promise.resolve([{ count: mockDatabase.emails.size }])
          }
          return Promise.resolve([])
        }
      })
    }),
    insert: (table: any) => ({
      values: (data: any) => {
        console.log('Mock insert:', data)

        // 模拟插入邮箱
        if (table === 'emails' || (data.address && data.userId)) {
          const emailId = `email-${Date.now()}`
          const newEmail = {
            id: emailId,
            address: data.address,
            userId: data.userId,
            createdAt: new Date(),
            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)
          }
          mockDatabase.emails.set(emailId, newEmail)
          return Promise.resolve({ success: true, insertedId: emailId })
        }

        return Promise.resolve({ success: true, insertedId: 'mock-id' })
      }
    }),
    update: (table: any) => ({
      set: (data: any) => ({
        where: (condition: any) => {
          console.log('Mock update:', data)
          return Promise.resolve({ success: true })
        }
      })
    }),
    delete: (table: any) => ({
      where: (condition: any) => {
        console.log('Mock delete')
        return Promise.resolve({ success: true, changes: 0 })
      }
    })
  }

  return mockDb as any
}

export type Db = ReturnType<typeof createDb>
