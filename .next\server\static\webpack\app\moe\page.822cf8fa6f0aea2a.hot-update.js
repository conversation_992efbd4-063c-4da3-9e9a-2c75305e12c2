"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/moe/page",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 使用单例模式确保数据持久化\nclass MockDatabase {\n    constructor(){\n        this.users = new Map();\n        this.emails = new Map();\n        this.messages = new Map();\n        this.emailCredentials = new Map();\n        this.roles = new Map();\n        this.userRoles = new Map();\n        this.apiKeys = new Map();\n        this.accounts = new Map();\n        this.sessions = new Map();\n        this.verificationTokens = new Map();\n        this.webhooks = new Map();\n    // 私有构造函数，确保单例\n    }\n    static getInstance() {\n        if (!MockDatabase.instance) {\n            MockDatabase.instance = new MockDatabase();\n        }\n        return MockDatabase.instance;\n    }\n}\n// 导出单例实例\nconst mockDatabase = MockDatabase.getInstance();\n// 初始化一些示例数据\nfunction initMockData() {\n    // 先从文件加载数据\n    loadFromFile();\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    saveToFile();\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});