CREATE TABLE `email_credentials` (
	`id` text PRIMARY KEY NOT NULL,
	`email_id` text NOT NULL,
	`credential` text NOT NULL,
	`created_at` integer NOT NULL,
	`expires_at` integer NOT NULL,
	`last_accessed_at` integer,
	`access_count` integer DEFAULT 0 NOT NULL,
	`enabled` integer DEFAULT true NOT NULL,
	FOREIGN KEY (`email_id`) REFERENCES `email`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `email_credentials_credential_unique` ON `email_credentials` (`credential`);
--> statement-breakpoint
CREATE INDEX `email_credentials_credential_idx` ON `email_credentials` (`credential`);
--> statement-breakpoint
CREATE INDEX `email_credentials_email_id_idx` ON `email_credentials` (`email_id`);
--> statement-breakpoint
CREATE INDEX `email_credentials_expires_at_idx` ON `email_credentials` (`expires_at`);
