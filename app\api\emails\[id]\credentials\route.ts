import { NextResponse } from "next/server"
import { createDb } from "@/lib/db"
import { emails } from "@/lib/schema"
import { eq, and } from "drizzle-orm"
import { getUserId } from "@/lib/apiKey"
import { generateEmailCredential, getEmailCredentials } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

const createCredentialSchema = z.object({
  expiryTime: z.number().optional().default(7 * 24 * 60 * 60 * 1000), // 默认7天
})

/**
 * 获取邮箱的所有凭证
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = await getUserId()
  
  try {
    const db = createDb()
    const { id } = await params
    
    // 开发环境演示数据
    const demoEmails = [
      {
        id: 'demo-email-1',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 60 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000)
      },
      {
        id: 'demo-email-2',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000)
      },
      {
        id: 'demo-email-3',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 10 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000)
      }
    ]

    // 验证邮箱所有权
    let email = await db.query.emails.findFirst({
      where: and(
        eq(emails.id, id),
        eq(emails.userId, userId!)
      )
    })

    // 如果数据库中没有，检查是否是演示邮箱
    if (!email) {
      email = demoEmails.find(e => e.id === id && e.userId === userId)
    }

    if (!email) {
      return NextResponse.json(
        { error: "邮箱不存在或无权限访问" },
        { status: 403 }
      )
    }
    
    const credentials = await getEmailCredentials(id)
    
    return NextResponse.json({
      credentials: credentials.map(cred => ({
        id: cred.id,
        credential: cred.credential,
        createdAt: cred.createdAt.toISOString(),
        expiresAt: cred.expiresAt.toISOString(),
        lastAccessedAt: cred.lastAccessedAt?.toISOString() || null,
        accessCount: cred.accessCount,
        enabled: cred.enabled
      }))
    })
  } catch (error) {
    console.error('Failed to fetch email credentials:', error)
    return NextResponse.json(
      { error: "获取凭证失败" },
      { status: 500 }
    )
  }
}

/**
 * 为邮箱生成新凭证
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = await getUserId()
  
  try {
    const db = createDb()
    const { id } = await params
    const body = await request.json()
    const { expiryTime } = createCredentialSchema.parse(body)
    
    // 开发环境演示数据
    const demoEmails = [
      {
        id: 'demo-email-1',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 60 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000)
      },
      {
        id: 'demo-email-2',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000)
      },
      {
        id: 'demo-email-3',
        address: '<EMAIL>',
        userId: userId!,
        createdAt: new Date(Date.now() - 10 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000)
      }
    ]

    // 验证邮箱所有权
    let email = await db.query.emails.findFirst({
      where: and(
        eq(emails.id, id),
        eq(emails.userId, userId!)
      )
    })

    // 如果数据库中没有，检查是否是演示邮箱
    if (!email) {
      email = demoEmails.find(e => e.id === id && e.userId === userId)
    }

    if (!email) {
      return NextResponse.json(
        { error: "邮箱不存在或无权限访问" },
        { status: 403 }
      )
    }
    
    // 生成凭证
    const credential = await generateEmailCredential({
      emailId: id,
      expiryTime
    })
    
    return NextResponse.json({
      credential,
      message: "凭证生成成功"
    })
  } catch (error) {
    console.error('Failed to generate email credential:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "生成凭证失败" },
      { status: 500 }
    )
  }
}
