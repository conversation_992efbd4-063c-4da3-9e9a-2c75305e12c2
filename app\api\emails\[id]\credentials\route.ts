import { NextResponse } from "next/server"
import { createDb } from "@/lib/db"
import { emails } from "@/lib/schema"
import { eq, and } from "drizzle-orm"
import { getUserId } from "@/lib/apiKey"
import { generateEmailCredential, getEmailCredentials } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

const createCredentialSchema = z.object({
  expiryTime: z.number().optional().default(7 * 24 * 60 * 60 * 1000), // 默认7天
})

/**
 * 获取邮箱的所有凭证
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = await getUserId()
  
  try {
    const db = createDb()
    const { id } = await params
    
    // 验证邮箱所有权
    const email = await db.query.emails.findFirst({
      where: and(
        eq(emails.id, id),
        eq(emails.userId, userId!)
      )
    })
    
    if (!email) {
      return NextResponse.json(
        { error: "邮箱不存在或无权限访问" },
        { status: 403 }
      )
    }
    
    const credentials = await getEmailCredentials(id)
    
    return NextResponse.json({
      credentials: credentials.map(cred => ({
        id: cred.id,
        credential: cred.credential,
        createdAt: cred.createdAt.toISOString(),
        expiresAt: cred.expiresAt.toISOString(),
        lastAccessedAt: cred.lastAccessedAt?.toISOString() || null,
        accessCount: cred.accessCount,
        enabled: cred.enabled
      }))
    })
  } catch (error) {
    console.error('Failed to fetch email credentials:', error)
    return NextResponse.json(
      { error: "获取凭证失败" },
      { status: 500 }
    )
  }
}

/**
 * 为邮箱生成新凭证
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = await getUserId()
  
  try {
    const db = createDb()
    const { id } = await params
    const body = await request.json()
    const { expiryTime } = createCredentialSchema.parse(body)
    
    // 验证邮箱所有权
    const email = await db.query.emails.findFirst({
      where: and(
        eq(emails.id, id),
        eq(emails.userId, userId!)
      )
    })
    
    if (!email) {
      return NextResponse.json(
        { error: "邮箱不存在或无权限访问" },
        { status: 403 }
      )
    }
    
    // 生成凭证
    const credential = await generateEmailCredential({
      emailId: id,
      expiryTime
    })
    
    return NextResponse.json({
      credential,
      message: "凭证生成成功"
    })
  } catch (error) {
    console.error('Failed to generate email credential:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "生成凭证失败" },
      { status: 500 }
    )
  }
}
