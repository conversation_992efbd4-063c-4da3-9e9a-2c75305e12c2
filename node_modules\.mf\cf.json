{"clientTcpRtt": 218, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 9808, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "0HciHL/XFiLjela2yEtKqkkMNMDKYComz4nJ9a9ibdE=", "tlsExportedAuthenticator": {"clientFinished": "4b39e6a5e0d26558c2364a727752854c904b8c84493577d50e768d22c8bc6feed62426b29beed351b4d156097ab44684", "clientHandshake": "2d603f416ea7dff8139c1adabfccf75fbaa41c34d036919e007cd7485bea90584dd4fde8411655d44255e818ac6405ec", "serverHandshake": "baaee936251c50911ac9de12c62cad89a69c378491efbb33e9ea87e4844c438fa5a4f9a5c0ace9f1c3ea9ae36e531f02", "serverFinished": "3660d9f3640d15e9992ea35080eac71627fa29bd9b02228c593a3517d6d6e58c948bac7a0f5ddcf4f80ff67cf7a4371a"}, "tlsClientHelloLength": "386", "colo": "HKG", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "China Mobile Communications Corporation", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}