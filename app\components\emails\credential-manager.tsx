"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import { Copy, Key, Clock, Eye, Plus } from "lucide-react"
// import { formatDistanceToNow } from "date-fns"
// import { zhCN } from "date-fns/locale"

interface EmailCredential {
  id: string
  credential: string
  createdAt: string
  expiresAt: string
  lastAccessedAt: string | null
  accessCount: number
  enabled: boolean
}

interface CredentialManagerProps {
  emailId: string
  emailAddress: string
}

const EXPIRY_OPTIONS = [
  { label: "1小时", value: 1 * 60 * 60 * 1000 },
  { label: "1天", value: 24 * 60 * 60 * 1000 },
  { label: "3天", value: 3 * 24 * 60 * 60 * 1000 },
  { label: "7天", value: 7 * 24 * 60 * 60 * 1000 },
  { label: "30天", value: 30 * 24 * 60 * 60 * 1000 },
]

export function CredentialManager({ emailId, emailAddress }: CredentialManagerProps) {
  const [credentials, setCredentials] = useState<EmailCredential[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [expiryTime, setExpiryTime] = useState(7 * 24 * 60 * 60 * 1000) // 默认7天
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  const fetchCredentials = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/emails/${emailId}/credentials`)
      const data = await response.json()
      
      if (response.ok) {
        setCredentials(data.credentials)
      } else {
        toast({
          title: "获取凭证失败",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "获取凭证失败",
        description: "网络错误，请重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const createCredential = async () => {
    setCreating(true)
    try {
      const response = await fetch(`/api/emails/${emailId}/credentials`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ expiryTime }),
      })
      
      const data = await response.json()
      
      if (response.ok) {
        toast({
          title: "凭证生成成功",
          description: "新的访问凭证已生成",
        })
        setShowCreateDialog(false)
        fetchCredentials()
        
        // 复制凭证到剪贴板
        await navigator.clipboard.writeText(data.credential)
        toast({
          title: "凭证已复制",
          description: "凭证已复制到剪贴板",
        })
      } else {
        toast({
          title: "生成凭证失败",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "生成凭证失败",
        description: "网络错误，请重试",
        variant: "destructive",
      })
    } finally {
      setCreating(false)
    }
  }

  const copyCredential = async (credential: string) => {
    try {
      await navigator.clipboard.writeText(credential)
      toast({
        title: "凭证已复制",
        description: "凭证已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  const copyAccessUrl = async (credential: string) => {
    const url = `${window.location.origin}/api/credentials/${credential}`
    try {
      await navigator.clipboard.writeText(url)
      toast({
        title: "访问链接已复制",
        description: "API访问链接已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    fetchCredentials()
  }, [emailId])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              访问凭证
            </CardTitle>
            <CardDescription>
              为邮箱 {emailAddress} 管理访问凭证
            </CardDescription>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                生成凭证
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>生成新凭证</DialogTitle>
                <DialogDescription>
                  为此邮箱生成一个新的访问凭证
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="expiry">有效期</Label>
                  <Select value={expiryTime.toString()} onValueChange={(value) => setExpiryTime(Number(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPIRY_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    取消
                  </Button>
                  <Button onClick={createCredential} disabled={creating}>
                    {creating ? "生成中..." : "生成凭证"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">加载中...</div>
        ) : credentials.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            暂无凭证，点击上方按钮生成
          </div>
        ) : (
          <div className="space-y-3">
            {credentials.map((cred) => (
              <div key={cred.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant={cred.enabled ? "default" : "secondary"}>
                      {cred.enabled ? "有效" : "已禁用"}
                    </Badge>
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {new Date(cred.expiresAt) > new Date() ? "有效" : "已过期"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Eye className="h-3 w-3" />
                    访问 {cred.accessCount} 次
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div>
                    <Label className="text-xs">凭证</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        value={cred.credential}
                        readOnly
                        className="font-mono text-xs"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyCredential(cred.credential)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyAccessUrl(cred.credential)}
                      className="flex-1"
                    >
                      复制API链接
                    </Button>
                  </div>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  创建于 {new Date(cred.createdAt).toLocaleString()}
                  {cred.lastAccessedAt && (
                    <span className="ml-2">
                      最后访问 {new Date(cred.lastAccessedAt).toLocaleString()}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
