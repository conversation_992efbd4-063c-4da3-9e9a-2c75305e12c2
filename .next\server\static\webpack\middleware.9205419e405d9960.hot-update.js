"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 简单的内存数据库\nconst mockDatabase = {\n    users: new Map(),\n    emails: new Map(),\n    messages: new Map(),\n    emailCredentials: new Map(),\n    roles: new Map(),\n    userRoles: new Map(),\n    apiKeys: new Map(),\n    accounts: new Map(),\n    sessions: new Map(),\n    verificationTokens: new Map(),\n    webhooks: new Map()\n};\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    // 使用模拟数据库\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            lastInsertRowid: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        lastInsertRowid: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});