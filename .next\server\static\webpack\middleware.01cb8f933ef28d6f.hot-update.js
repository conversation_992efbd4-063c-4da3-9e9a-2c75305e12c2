"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 简单的内存数据库\nconst mockDatabase = {\n    users: new Map(),\n    emails: new Map(),\n    messages: new Map(),\n    emailCredentials: new Map(),\n    roles: new Map(),\n    userRoles: new Map(),\n    apiKeys: new Map(),\n    accounts: new Map(),\n    sessions: new Map(),\n    verificationTokens: new Map(),\n    webhooks: new Map()\n};\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    try {\n        // 尝试使用真实的 SQLite 数据库\n        return getDatabase();\n    } catch (error) {\n        console.error('Failed to create database:', error);\n        console.log('Falling back to mock database');\n        // 如果失败，回退到模拟数据库\n        const mockDb = {\n            query: {\n                users: {\n                    findFirst: (options)=>{\n                        const users = Array.from(mockDatabase.users.values());\n                        return Promise.resolve(users[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n                },\n                emails: {\n                    findFirst: (options)=>{\n                        const emails = Array.from(mockDatabase.emails.values());\n                        return Promise.resolve(emails[0] || null);\n                    },\n                    findMany: (options)=>{\n                        const emails = Array.from(mockDatabase.emails.values());\n                        // 简单的过滤和排序\n                        return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                    }\n                },\n                emailCredentials: {\n                    findFirst: (options)=>{\n                        const credentials = Array.from(mockDatabase.emailCredentials.values());\n                        return Promise.resolve(credentials[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n                },\n                messages: {\n                    findFirst: (options)=>{\n                        const messages = Array.from(mockDatabase.messages.values());\n                        return Promise.resolve(messages[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n                },\n                userRoles: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                roles: {\n                    findFirst: (options)=>{\n                        const roles = Array.from(mockDatabase.roles.values());\n                        return Promise.resolve(roles[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n                },\n                apiKeys: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                accounts: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                sessions: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                verificationTokens: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                webhooks: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                }\n            },\n            select: (fields)=>({\n                    from: (table)=>({\n                            where: (condition)=>{\n                                // 模拟 count 查询\n                                if (fields.count) {\n                                    return Promise.resolve([\n                                        {\n                                            count: mockDatabase.emails.size\n                                        }\n                                    ]);\n                                }\n                                return Promise.resolve([]);\n                            }\n                        })\n                }),\n            insert: (table)=>({\n                    values: (data)=>{\n                        console.log('Mock insert:', data);\n                        // 模拟插入邮箱\n                        if (table === 'emails' || data.address && data.userId) {\n                            const emailId = `email-${Date.now()}`;\n                            const newEmail = {\n                                id: emailId,\n                                address: data.address,\n                                userId: data.userId,\n                                createdAt: new Date(),\n                                expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                            };\n                            mockDatabase.emails.set(emailId, newEmail);\n                            return Promise.resolve({\n                                success: true,\n                                insertedId: emailId\n                            });\n                        }\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: 'mock-id'\n                        });\n                    }\n                }),\n            update: (table)=>({\n                    set: (data)=>({\n                            where: (condition)=>{\n                                console.log('Mock update:', data);\n                                return Promise.resolve({\n                                    success: true\n                                });\n                            }\n                        })\n                }),\n            delete: (table)=>({\n                    where: (condition)=>{\n                        console.log('Mock delete');\n                        return Promise.resolve({\n                            success: true,\n                            changes: 0\n                        });\n                    }\n                })\n        };\n        return mockDb;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});