{"version": 3, "file": "browser-rendering.d.ts", "sourceRoot": "", "sources": ["../../src/resources/browser-rendering/browser-rendering.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,WAAW,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,MAAM,WAAW,CAAC;AAChF,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,QAAQ,CAAC;AACpE,OAAO,KAAK,QAAQ,MAAM,SAAS,CAAC;AACpC,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACtE,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AACpF,OAAO,KAAK,MAAM,MAAM,OAAO,CAAC;AAChC,OAAO,EAAE,GAAG,EAAE,eAAe,EAAE,MAAM,OAAO,CAAC;AAC7C,OAAO,KAAK,SAAS,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,MAAM,UAAU,CAAC;AAC5E,OAAO,KAAK,aAAa,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,MAAM,cAAc,CAAC;AAC5F,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AAEpF,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,OAAO,EAAE,UAAU,CAAC,OAAO,CAAwC;IACnE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAgC;IAC/C,MAAM,EAAE,SAAS,CAAC,MAAM,CAAsC;IAC9D,UAAU,EAAE,aAAa,CAAC,UAAU,CAA8C;IAClF,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAA0C;IACxE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAkC;IACpD,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAoC;IACzD,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAA0C;CACzE;AAWD,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,OAAO,EACL,OAAO,IAAI,OAAO,EAClB,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;IAEF,OAAO,EAAE,GAAG,IAAI,GAAG,EAAE,KAAK,eAAe,IAAI,eAAe,EAAE,CAAC;IAE/D,OAAO,EACL,MAAM,IAAI,MAAM,EAChB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;IAEF,OAAO,EACL,UAAU,IAAI,UAAU,EACxB,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,sBAAsB,IAAI,sBAAsB,GACtD,CAAC;IAEF,OAAO,EACL,QAAQ,IAAI,QAAQ,EACpB,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,oBAAoB,IAAI,oBAAoB,GAClD,CAAC;IAEF,OAAO,EACL,IAAI,IAAI,IAAI,EACZ,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;IAEF,OAAO,EACL,KAAK,IAAI,KAAK,EACd,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;IAEF,OAAO,EACL,QAAQ,IAAI,QAAQ,EACpB,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,oBAAoB,IAAI,oBAAoB,GAClD,CAAC;CACH"}