"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 使用单例模式确保数据持久化\nclass MockDatabase {\n    constructor(){\n        this.users = new Map();\n        this.emails = new Map();\n        this.messages = new Map();\n        this.emailCredentials = new Map();\n        this.roles = new Map();\n        this.userRoles = new Map();\n        this.apiKeys = new Map();\n        this.accounts = new Map();\n        this.sessions = new Map();\n        this.verificationTokens = new Map();\n        this.webhooks = new Map();\n    // 私有构造函数，确保单例\n    }\n    static getInstance() {\n        if (!MockDatabase.instance) {\n            MockDatabase.instance = new MockDatabase();\n        }\n        return MockDatabase.instance;\n    }\n}\n// 导出单例实例\nconst mockDatabase = MockDatabase.getInstance();\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});