"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 简单的内存数据库\nconst mockDatabase = {\n    users: new Map(),\n    emails: new Map(),\n    messages: new Map(),\n    emailCredentials: new Map(),\n    roles: new Map(),\n    userRoles: new Map(),\n    apiKeys: new Map(),\n    accounts: new Map(),\n    sessions: new Map(),\n    verificationTokens: new Map(),\n    webhooks: new Map()\n};\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    try {\n        // 尝试使用真实的 SQLite 数据库\n        return getDatabase();\n    } catch (error) {\n        console.error('Failed to create database:', error);\n        console.log('Falling back to mock database');\n        // 如果失败，回退到模拟数据库\n        const mockDb = {\n            query: {\n                users: {\n                    findFirst: (options)=>{\n                        const users = Array.from(mockDatabase.users.values());\n                        return Promise.resolve(users[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n                },\n                emails: {\n                    findFirst: (options)=>{\n                        const emails = Array.from(mockDatabase.emails.values());\n                        return Promise.resolve(emails[0] || null);\n                    },\n                    findMany: (options)=>{\n                        const emails = Array.from(mockDatabase.emails.values());\n                        // 简单的过滤和排序\n                        return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                    }\n                },\n                emailCredentials: {\n                    findFirst: (options)=>{\n                        const credentials = Array.from(mockDatabase.emailCredentials.values());\n                        return Promise.resolve(credentials[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n                },\n                messages: {\n                    findFirst: (options)=>{\n                        const messages = Array.from(mockDatabase.messages.values());\n                        return Promise.resolve(messages[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n                },\n                userRoles: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                roles: {\n                    findFirst: (options)=>{\n                        const roles = Array.from(mockDatabase.roles.values());\n                        return Promise.resolve(roles[0] || null);\n                    },\n                    findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n                },\n                apiKeys: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                accounts: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                sessions: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                verificationTokens: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                },\n                webhooks: {\n                    findFirst: ()=>Promise.resolve(null),\n                    findMany: ()=>Promise.resolve([])\n                }\n            },\n            select: (fields)=>({\n                    from: (table)=>({\n                            where: (condition)=>{\n                                // 模拟 count 查询\n                                if (fields.count) {\n                                    return Promise.resolve([\n                                        {\n                                            count: mockDatabase.emails.size\n                                        }\n                                    ]);\n                                }\n                                return Promise.resolve([]);\n                            }\n                        })\n                }),\n            insert: (table)=>({\n                    values: (data)=>{\n                        console.log('Mock insert:', data);\n                        // 模拟插入邮箱\n                        if (table === 'emails' || data.address && data.userId) {\n                            const emailId = `email-${Date.now()}`;\n                            const newEmail = {\n                                id: emailId,\n                                address: data.address,\n                                userId: data.userId,\n                                createdAt: new Date(),\n                                expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                            };\n                            mockDatabase.emails.set(emailId, newEmail);\n                            return Promise.resolve({\n                                success: true,\n                                insertedId: emailId\n                            });\n                        }\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: 'mock-id'\n                        });\n                    }\n                }),\n            update: (table)=>({\n                    set: (data)=>({\n                            where: (condition)=>{\n                                console.log('Mock update:', data);\n                                return Promise.resolve({\n                                    success: true\n                                });\n                            }\n                        })\n                }),\n            delete: (table)=>({\n                    where: (condition)=>{\n                        console.log('Mock delete');\n                        return Promise.resolve({\n                            success: true,\n                            changes: 0\n                        });\n                    }\n                })\n        };\n        return mockDb;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});