"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/api/emails/route",{

/***/ "(rsc)/./app/api/emails/route.ts":
/*!*********************************!*\
  !*** ./app/api/emails/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_cursor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cursor */ \"(rsc)/./app/lib/cursor.ts\");\n/* harmony import */ var _lib_apiKey__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/apiKey */ \"(rsc)/./app/lib/apiKey.ts\");\n\n\n\n\nconst runtime = \"edge\";\nconst PAGE_SIZE = 20;\nasync function GET(request) {\n    const userId = await (0,_lib_apiKey__WEBPACK_IMPORTED_MODULE_3__.getUserId)();\n    const { searchParams } = new URL(request.url);\n    const cursor = searchParams.get('cursor');\n    const db = (0,_lib_db__WEBPACK_IMPORTED_MODULE_0__.createDb)();\n    try {\n        // 开发环境演示数据 + 动态创建的邮箱\n        const allEmails = Array.from(_lib_db__WEBPACK_IMPORTED_MODULE_0__.mockDatabase.emails.values());\n        console.log('🔍 获取邮箱列表 - 用户ID:', userId);\n        console.log('🔍 数据库中所有邮箱:', allEmails.length);\n        // 固定的演示邮箱\n        const demoEmails = [\n            {\n                id: 'demo-email-1',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 60 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000) // 23小时后\n            },\n            {\n                id: 'demo-email-2',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 30 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000) // 23.5小时后\n            },\n            {\n                id: 'demo-email-3',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 10 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000) // 23.8小时后\n            }\n        ];\n        // 合并演示邮箱和动态创建的邮箱\n        const combinedEmails = [\n            ...demoEmails,\n            ...allEmails\n        ];\n        const userEmails = combinedEmails.filter((email)=>email.userId === userId && new Date(email.expiresAt) > new Date()).sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        console.log('🔍 用户邮箱:', userEmails);\n        const totalCount = userEmails.length;\n        // 简化分页逻辑\n        let startIndex = 0;\n        if (cursor) {\n            try {\n                const { timestamp, id } = (0,_lib_cursor__WEBPACK_IMPORTED_MODULE_2__.decodeCursor)(cursor);\n                startIndex = userEmails.findIndex((email)=>new Date(email.createdAt).getTime() < timestamp || new Date(email.createdAt).getTime() === timestamp && email.id < id);\n                if (startIndex === -1) startIndex = userEmails.length;\n            } catch (e) {\n                startIndex = 0;\n            }\n        }\n        const emailList = userEmails.slice(startIndex, startIndex + PAGE_SIZE);\n        const hasMore = startIndex + PAGE_SIZE < userEmails.length;\n        const nextCursor = hasMore && emailList.length > 0 ? (0,_lib_cursor__WEBPACK_IMPORTED_MODULE_2__.encodeCursor)(new Date(emailList[emailList.length - 1].createdAt).getTime(), emailList[emailList.length - 1].id) : null;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            emails: emailList,\n            nextCursor,\n            total: totalCount\n        });\n    } catch (error) {\n        console.error('Failed to fetch user emails:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to fetch emails\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/emails/route.ts\n");

/***/ })

});