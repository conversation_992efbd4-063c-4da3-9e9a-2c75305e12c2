import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { 
  generateEmailCredential, 
  validateCredential, 
  getEmailCredentials,
  disableCredential,
  cleanupExpiredCredentials 
} from '../app/lib/emailCredentials'

// Mock database and dependencies
const mockDb = {
  query: {
    emails: {
      findFirst: vi.fn()
    },
    emailCredentials: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    }
  },
  insert: vi.fn().mockReturnValue({
    values: vi.fn().mockReturnThis()
  }),
  update: vi.fn().mockReturnValue({
    set: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis()
  }),
  delete: vi.fn().mockReturnValue({
    where: vi.fn().mockReturnThis()
  })
}

// Mock createDb
vi.mock('../app/lib/db', () => ({
  createDb: () => mockDb
}))

describe('Email Credentials', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('generateEmailCredential', () => {
    it('should generate credential for valid email', async () => {
      // Mock email exists and is not expired
      mockDb.query.emails.findFirst.mockResolvedValue({
        id: 'email-1',
        address: '<EMAIL>',
        expiresAt: new Date(Date.now() + 86400000) // 1 day from now
      })

      // Mock no existing credential with same value
      mockDb.query.emailCredentials.findFirst.mockResolvedValue(null)

      const credential = await generateEmailCredential({
        emailId: 'email-1'
      })

      expect(credential).toBeDefined()
      expect(typeof credential).toBe('string')
      expect(credential.length).toBe(32)
      expect(mockDb.insert).toHaveBeenCalled()
    })

    it('should throw error for non-existent email', async () => {
      mockDb.query.emails.findFirst.mockResolvedValue(null)

      await expect(generateEmailCredential({
        emailId: 'non-existent'
      })).rejects.toThrow('邮箱不存在')
    })

    it('should throw error for expired email', async () => {
      mockDb.query.emails.findFirst.mockResolvedValue({
        id: 'email-1',
        address: '<EMAIL>',
        expiresAt: new Date(Date.now() - 86400000) // 1 day ago
      })

      await expect(generateEmailCredential({
        emailId: 'email-1'
      })).rejects.toThrow('邮箱已过期')
    })
  })

  describe('validateCredential', () => {
    it('should validate and return credential info for valid credential', async () => {
      const mockCredential = {
        id: 'cred-1',
        emailId: 'email-1',
        credential: 'test-credential',
        enabled: true,
        expiresAt: new Date(Date.now() + 86400000),
        accessCount: 0,
        email: {
          id: 'email-1',
          address: '<EMAIL>',
          expiresAt: new Date(Date.now() + 86400000)
        }
      }

      mockDb.query.emailCredentials.findFirst.mockResolvedValue(mockCredential)

      const result = await validateCredential('test-credential')

      expect(result).toBeDefined()
      expect(result?.credential.id).toBe('cred-1')
      expect(result?.email.address).toBe('<EMAIL>')
      expect(mockDb.update).toHaveBeenCalled() // Should update access count
    })

    it('should return null for invalid credential', async () => {
      mockDb.query.emailCredentials.findFirst.mockResolvedValue(null)

      const result = await validateCredential('invalid-credential')

      expect(result).toBeNull()
    })

    it('should return null for credential with expired email', async () => {
      const mockCredential = {
        id: 'cred-1',
        emailId: 'email-1',
        credential: 'test-credential',
        enabled: true,
        expiresAt: new Date(Date.now() + 86400000),
        accessCount: 0,
        email: {
          id: 'email-1',
          address: '<EMAIL>',
          expiresAt: new Date(Date.now() - 86400000) // Expired email
        }
      }

      mockDb.query.emailCredentials.findFirst.mockResolvedValue(mockCredential)

      const result = await validateCredential('test-credential')

      expect(result).toBeNull()
    })
  })

  describe('getEmailCredentials', () => {
    it('should return credentials for email', async () => {
      const mockCredentials = [
        {
          id: 'cred-1',
          credential: 'test-credential-1',
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 86400000),
          enabled: true,
          accessCount: 5
        },
        {
          id: 'cred-2',
          credential: 'test-credential-2',
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 86400000),
          enabled: false,
          accessCount: 0
        }
      ]

      mockDb.query.emailCredentials.findMany.mockResolvedValue(mockCredentials)

      const result = await getEmailCredentials('email-1')

      expect(result).toHaveLength(2)
      expect(result[0].id).toBe('cred-1')
      expect(result[1].enabled).toBe(false)
    })
  })

  describe('disableCredential', () => {
    it('should disable credential successfully', async () => {
      mockDb.update().set().where.mockResolvedValue({ success: true })

      const result = await disableCredential('cred-1')

      expect(result).toBe(true)
      expect(mockDb.update).toHaveBeenCalled()
    })

    it('should return false on failure', async () => {
      mockDb.update().set().where.mockResolvedValue({ success: false })

      const result = await disableCredential('cred-1')

      expect(result).toBe(false)
    })
  })

  describe('cleanupExpiredCredentials', () => {
    it('should return number of cleaned up credentials', async () => {
      mockDb.delete().where.mockResolvedValue({ changes: 5 })

      const result = await cleanupExpiredCredentials()

      expect(result).toBe(5)
      expect(mockDb.delete).toHaveBeenCalled()
    })

    it('should return 0 when no changes', async () => {
      mockDb.delete().where.mockResolvedValue({ changes: null })

      const result = await cleanupExpiredCredentials()

      expect(result).toBe(0)
    })
  })
})
