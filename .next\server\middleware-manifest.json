{"version": 3, "middleware": {}, "functions": {"/api/auth/[...auth]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/auth/[...auth]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/auth/[...auth]/route.js"], "name": "app/api/auth/[...auth]/route", "page": "/api/auth/[...auth]/route", "matchers": [{"regexp": "^/api/auth/(?<auth>.+?)$", "originalSource": "/api/auth/[...auth]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/api/auth/register/route": {"files": ["server/server-reference-manifest.js", "server/app/api/auth/register/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/auth/register/route.js"], "name": "app/api/auth/register/route", "page": "/api/auth/register/route", "matchers": [{"regexp": "^/api/auth/register$", "originalSource": "/api/auth/register"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/page": {"files": ["server/server-reference-manifest.js", "server/app/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/page.js"], "name": "app/page", "page": "/page", "matchers": [{"regexp": "^/$", "originalSource": "/"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/login/page": {"files": ["server/server-reference-manifest.js", "server/app/login/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/login/page.js"], "name": "app/login/page", "page": "/login/page", "matchers": [{"regexp": "^/login$", "originalSource": "/login"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}}, "sortedMiddleware": []}