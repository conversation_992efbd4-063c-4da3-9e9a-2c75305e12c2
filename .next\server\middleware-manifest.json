{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}}, "functions": {"/page": {"files": ["server/server-reference-manifest.js", "server/app/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/page.js"], "name": "app/page", "page": "/page", "matchers": [{"regexp": "^/$", "originalSource": "/"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/login/page": {"files": ["server/server-reference-manifest.js", "server/app/login/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/login/page.js"], "name": "app/login/page", "page": "/login/page", "matchers": [{"regexp": "^/login$", "originalSource": "/login"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}, "/moe/page": {"files": ["server/server-reference-manifest.js", "server/app/moe/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/moe/page.js"], "name": "app/moe/page", "page": "/moe/page", "matchers": [{"regexp": "^/moe$", "originalSource": "/moe"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xuEgi5AGcMd01dKsfcmiTT05QAfxsfB23zD/y2QKtGQ="}}}, "sortedMiddleware": ["/"]}