"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 确保使用全局单例\nif (!__webpack_require__.g.__mockDatabase) {\n    __webpack_require__.g.__mockDatabase = {\n        users: new Map(),\n        emails: new Map(),\n        messages: new Map(),\n        emailCredentials: new Map(),\n        roles: new Map(),\n        userRoles: new Map(),\n        apiKeys: new Map(),\n        accounts: new Map(),\n        sessions: new Map(),\n        verificationTokens: new Map(),\n        webhooks: new Map()\n    };\n}\n// 导出全局数据库实例\nconst mockDatabase = __webpack_require__.g.__mockDatabase;\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    // 使用模拟数据库\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            lastInsertRowid: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        lastInsertRowid: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});