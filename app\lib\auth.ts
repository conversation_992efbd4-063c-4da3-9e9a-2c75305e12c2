import NextAuth from "next-auth"
import GitHub from "next-auth/providers/github"
import { DrizzleAdapter } from "@auth/drizzle-adapter"
import { createDb, Db } from "./db"
import { accounts, users, roles, userRoles } from "./schema"
import { eq } from "drizzle-orm"
import { getRequestContext } from "@cloudflare/next-on-pages"
import { Permission, hasPermission, ROLES, Role } from "./permissions"
import CredentialsProvider from "next-auth/providers/credentials"
import { hashPassword, comparePassword } from "@/lib/utils"
import { authSchema } from "@/lib/validation"
import { generateAvatarUrl } from "./avatar"
import { getUserId } from "./apiKey"

const ROLE_DESCRIPTIONS: Record<Role, string> = {
  [ROLES.EMPEROR]: "皇帝（网站所有者）",
  [ROLES.DUKE]: "公爵（超级用户）",
  [ROLES.KNIGHT]: "骑士（高级用户）",
  [ROLES.CIVILIAN]: "平民（普通用户）",
}

const getDefaultRole = async (): Promise<Role> => {
  try {
    const defaultRole = await getRequestContext().env.SITE_CONFIG.get("DEFAULT_ROLE")

    if (
      defaultRole === ROLES.DUKE ||
      defaultRole === ROLES.KNIGHT ||
      defaultRole === ROLES.CIVILIAN
    ) {
      return defaultRole as Role
    }
  } catch (error) {
    // 开发环境回退
    console.log('使用默认角色：平民')
  }

  return ROLES.CIVILIAN
}

async function findOrCreateRole(db: Db, roleName: Role) {
  let role = await db.query.roles.findFirst({
    where: eq(roles.name, roleName),
  })

  if (!role) {
    const [newRole] = await db.insert(roles)
      .values({
        name: roleName,
        description: ROLE_DESCRIPTIONS[roleName],
      })
      .returning()
    role = newRole
  }

  return role
}

export async function assignRoleToUser(db: Db, userId: string, roleId: string) {
  await db.delete(userRoles)
    .where(eq(userRoles.userId, userId))

  await db.insert(userRoles)
    .values({
      userId,
      roleId,
    })
}

export async function getUserRole(userId: string) {
  const db = createDb()
  const userRoleRecords = await db.query.userRoles.findMany({
    where: eq(userRoles.userId, userId),
    with: { role: true },
  })
  return userRoleRecords[0].role.name
}

export async function checkPermission(permission: Permission) {
  const userId = await getUserId()

  if (!userId) return false

  const db = createDb()
  const userRoleRecords = await db.query.userRoles.findMany({
    where: eq(userRoles.userId, userId),
    with: { role: true },
  })

  const userRoleNames = userRoleRecords.map(ur => ur.role.name)
  return hasPermission(userRoleNames as Role[], permission)
}

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut
} = NextAuth(() => ({
  secret: process.env.AUTH_SECRET,
  // 开发环境暂时禁用 Drizzle 适配器
  // adapter: DrizzleAdapter(createDb(), {
  //   usersTable: users,
  //   accountsTable: accounts,
  // }),
  providers: [
    GitHub({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "用户名", type: "text", placeholder: "请输入用户名" },
        password: { label: "密码", type: "password", placeholder: "请输入密码" },
      },
      async authorize(credentials) {
        if (!credentials) {
          throw new Error("请输入用户名和密码")
        }

        const { username, password } = credentials

        try {
          authSchema.parse({ username, password })
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          throw new Error("输入格式不正确")
        }

        // 开发环境简化验证
        if (username === 'demo' && password === 'demo12345678') {
          return {
            id: 'demo-user',
            name: 'Demo User',
            username: 'demo',
            email: '<EMAIL>',
            image: null
          }
        }

        // 添加更多演示账号
        if (username === 'admin' && password === 'admin12345678') {
          return {
            id: 'admin-user',
            name: 'Admin User',
            username: 'admin',
            email: '<EMAIL>',
            image: null
          }
        }

        throw new Error("用户名或密码错误")
      },
    }),
  ],
  events: {
    async signIn({ user }) {
      if (!user.id) return
      console.log('用户登录:', user.name || user.username)
    },
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.name = user.name || user.username
        token.username = user.username
        token.image = user.image || generateAvatarUrl(token.name as string)
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string
        session.user.name = token.name as string
        session.user.username = token.username as string
        session.user.image = token.image as string

        // 开发环境简化角色处理
        session.user.roles = [{
          name: 'civilian'
        }]
      }

      return session
    },
  },
  session: {
    strategy: "jwt",
  },
}))

export async function register(username: string, password: string) {
  // 开发环境简化注册逻辑
  console.log('注册请求:', { username, passwordLength: password.length })

  // 检查用户名长度
  if (username.length < 3) {
    throw new Error("用户名至少需要3个字符")
  }

  // 检查密码长度
  if (password.length < 8) {
    throw new Error("密码至少需要8个字符")
  }

  // 模拟检查用户名是否存在（开发环境允许注册）
  const existingUsers = ['admin', 'demo', 'test', 'user']
  if (existingUsers.includes(username.toLowerCase())) {
    throw new Error("用户名已存在，请尝试其他用户名")
  }

  // 模拟成功注册
  const user = {
    id: `user-${Date.now()}`,
    username,
    name: username,
    email: `${username}@moemail.app`,
    image: null,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  console.log('注册成功:', user)
  return user
}
