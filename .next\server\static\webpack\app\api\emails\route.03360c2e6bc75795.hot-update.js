"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/api/emails/route",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 确保使用全局单例\nif (!__webpack_require__.g.__mockDatabase) {\n    __webpack_require__.g.__mockDatabase = {\n        users: new Map(),\n        emails: new Map(),\n        messages: new Map(),\n        emailCredentials: new Map(),\n        roles: new Map(),\n        userRoles: new Map(),\n        apiKeys: new Map(),\n        accounts: new Map(),\n        sessions: new Map(),\n        verificationTokens: new Map(),\n        webhooks: new Map()\n    };\n}\n// 导出全局数据库实例\nconst mockDatabase = __webpack_require__.g.__mockDatabase;\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    // 使用模拟数据库\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            lastInsertRowid: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        lastInsertRowid: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});