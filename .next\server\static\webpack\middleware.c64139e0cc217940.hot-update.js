"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 使用全局变量确保数据在热重载时保持\nif (!__webpack_require__.g.__mockDatabase) {\n    __webpack_require__.g.__mockDatabase = {\n        users: new Map(),\n        emails: new Map(),\n        messages: new Map(),\n        emailCredentials: new Map(),\n        roles: new Map(),\n        userRoles: new Map(),\n        apiKeys: new Map(),\n        accounts: new Map(),\n        sessions: new Map(),\n        verificationTokens: new Map(),\n        webhooks: new Map()\n    };\n}\n// 导出全局数据库实例\nconst mockDatabase = __webpack_require__.g.__mockDatabase;\n// 初始化一些示例数据\nfunction initMockData() {\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});