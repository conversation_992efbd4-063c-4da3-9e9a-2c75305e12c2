import * as schema from "./schema"

// 开发环境模拟数据库
const mockDatabase = {
  users: new Map(),
  emails: new Map(),
  messages: new Map(),
  emailCredentials: new Map(),
  roles: new Map(),
  userRoles: new Map(),
  apiKeys: new Map(),
  accounts: new Map(),
  sessions: new Map(),
  verificationTokens: new Map()
}

// 初始化一些示例数据
function initMockData() {
  // 创建示例用户
  const user = {
    id: 'user-1',
    name: 'Demo User',
    email: '<EMAIL>',
    emailVerified: null,
    image: null,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  mockDatabase.users.set(user.id, user)

  // 创建示例邮箱
  const email = {
    id: 'email-1',
    address: '<EMAIL>',
    userId: user.id,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
  }
  mockDatabase.emails.set(email.id, email)

  // 创建示例角色
  const roles = [
    { id: 'emperor', name: '皇帝', description: '最高权限', createdAt: new Date() },
    { id: 'duke', name: '公爵', description: '高级权限', createdAt: new Date() },
    { id: 'knight', name: '骑士', description: '中级权限', createdAt: new Date() },
    { id: 'civilian', name: '平民', description: '基础权限', createdAt: new Date() }
  ]
  roles.forEach(role => mockDatabase.roles.set(role.id, role))

  console.log('✅ 模拟数据库初始化完成')
}

// 初始化数据
initMockData()

export const createDb = () => {
  const mockDb = {
    query: {
      users: {
        findFirst: (options?: any) => {
          const users = Array.from(mockDatabase.users.values())
          return Promise.resolve(users[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.users.values()))
      },
      emails: {
        findFirst: (options?: any) => {
          const emails = Array.from(mockDatabase.emails.values())
          return Promise.resolve(emails[0] || null)
        },
        findMany: (options?: any) => {
          const emails = Array.from(mockDatabase.emails.values())
          // 简单的过滤和排序
          return Promise.resolve(emails.sort((a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          ))
        }
      },
      emailCredentials: {
        findFirst: (options?: any) => {
          const credentials = Array.from(mockDatabase.emailCredentials.values())
          return Promise.resolve(credentials[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))
      },
      messages: {
        findFirst: (options?: any) => {
          const messages = Array.from(mockDatabase.messages.values())
          return Promise.resolve(messages[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.messages.values()))
      },
      userRoles: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      roles: {
        findFirst: (options?: any) => {
          const roles = Array.from(mockDatabase.roles.values())
          return Promise.resolve(roles[0] || null)
        },
        findMany: () => Promise.resolve(Array.from(mockDatabase.roles.values()))
      },
      apiKeys: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      accounts: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      sessions: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      },
      verificationTokens: {
        findFirst: () => Promise.resolve(null),
        findMany: () => Promise.resolve([])
      }
    },
    select: (fields: any) => ({
      from: (table: any) => ({
        where: (condition: any) => {
          // 模拟 count 查询
          if (fields.count) {
            return Promise.resolve([{ count: mockDatabase.emails.size }])
          }
          return Promise.resolve([])
        }
      })
    }),
    insert: (table: any) => ({
      values: (data: any) => {
        console.log('Mock insert:', data)

        // 模拟插入邮箱
        if (table === 'emails' || (data.address && data.userId)) {
          const emailId = `email-${Date.now()}`
          const newEmail = {
            id: emailId,
            address: data.address,
            userId: data.userId,
            createdAt: new Date(),
            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)
          }
          mockDatabase.emails.set(emailId, newEmail)
          return Promise.resolve({ success: true, insertedId: emailId })
        }

        return Promise.resolve({ success: true, insertedId: 'mock-id' })
      }
    }),
    update: (table: any) => ({
      set: (data: any) => ({
        where: (condition: any) => {
          console.log('Mock update:', data)
          return Promise.resolve({ success: true })
        }
      })
    }),
    delete: (table: any) => ({
      where: (condition: any) => {
        console.log('Mock delete')
        return Promise.resolve({ success: true, changes: 0 })
      }
    })
  }

  return mockDb as any
}

export type Db = ReturnType<typeof createDb>
