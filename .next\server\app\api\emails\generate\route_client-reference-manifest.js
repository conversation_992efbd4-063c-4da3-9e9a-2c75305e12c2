globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/emails/generate/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{},"edgeSSRModuleMapping":{"(app-pages-browser)/./app/components/float-menu.tsx":{"*":{"id":"(ssr)/./app/components/float-menu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/theme/theme-provider.tsx":{"*":{"id":"(ssr)/./app/components/theme/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./app/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/auth/sign-button.tsx":{"*":{"id":"(ssr)/./app/components/auth/sign-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/emails/three-column-layout.tsx":{"*":{"id":"(ssr)/./app/components/emails/three-column-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/no-permission-dialog.tsx":{"*":{"id":"(ssr)/./app/components/no-permission-dialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/theme/theme-toggle.tsx":{"*":{"id":"(ssr)/./app/components/theme/theme-toggle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ui/logo.tsx":{"*":{"id":"(ssr)/./app/components/ui/logo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/shared/lib/app-router-context.shared-runtime.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/shared/lib/hooks-client-context.shared-runtime.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js":{"*":{"id":"(ssr)/./node_modules/next/dist/esm/shared/lib/server-inserted-html.shared-runtime.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/home/<USER>":{"*":{"id":"(ssr)/./app/components/home/<USER>","name":"*","chunks":[],"async":false}}},"clientModules":{"F:\\CODE\\Project\\Mail\\app\\components\\float-menu.tsx":{"id":"(app-pages-browser)/./app/components/float-menu.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\theme\\theme-provider.tsx":{"id":"(app-pages-browser)/./app/components/theme/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./app/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\font\\local\\target.css?{\"path\":\"app\\\\fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":\"../public/fonts/zpix.ttf\",\"variable\":\"--font-zpix\",\"display\":\"swap\"}],\"variableName\":\"zpix\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"app\\\\fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":\"../public/fonts/zpix.ttf\",\"variable\":\"--font-zpix\",\"display\":\"swap\"}],\"variableName\":\"zpix\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\auth\\sign-button.tsx":{"id":"(app-pages-browser)/./app/components/auth/sign-button.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\emails\\three-column-layout.tsx":{"id":"(app-pages-browser)/./app/components/emails/three-column-layout.tsx","name":"*","chunks":[],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\no-permission-dialog.tsx":{"id":"(app-pages-browser)/./app/components/no-permission-dialog.tsx","name":"*","chunks":[],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\theme\\theme-toggle.tsx":{"id":"(app-pages-browser)/./app/components/theme/theme-toggle.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\ui\\logo.tsx":{"id":"(app-pages-browser)/./app/components/ui/logo.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\dev-root-http-access-fallback-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\dev-root-http-access-fallback-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\client\\components\\router-reducer\\fetch-server-response.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\client\\components\\router-reducer\\fetch-server-response.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\shared\\lib\\app-router-context.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\shared\\lib\\hooks-client-context.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\shared\\lib\\hooks-client-context.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\shared\\lib\\server-inserted-html.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\node_modules\\next\\dist\\esm\\shared\\lib\\server-inserted-html.shared-runtime.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\CODE\\Project\\Mail\\app\\components\\home\\action-button.tsx":{"id":"(app-pages-browser)/./app/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false}},"entryCSSFiles":{"F:\\CODE\\Project\\Mail\\":[],"F:\\CODE\\Project\\Mail\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"F:\\CODE\\Project\\Mail\\app\\page":[],"F:\\CODE\\Project\\Mail\\app\\api\\emails\\route":[],"F:\\CODE\\Project\\Mail\\app\\api\\emails\\generate\\route":[]},"rscModuleMapping":{},"edgeRscModuleMapping":{}}