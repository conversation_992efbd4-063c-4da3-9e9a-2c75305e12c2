"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/api/auth/[...auth]/route",{

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb)\n/* harmony export */ });\n// 开发环境模拟数据库\nconst mockDatabase = {\n    users: new Map(),\n    emails: new Map(),\n    messages: new Map(),\n    emailCredentials: new Map(),\n    roles: new Map(),\n    userRoles: new Map(),\n    apiKeys: new Map(),\n    accounts: new Map(),\n    sessions: new Map(),\n    verificationTokens: new Map()\n};\n// 初始化一些示例数据\nfunction initMockData() {\n    // 创建示例用户\n    const user = {\n        id: 'user-1',\n        name: 'Demo User',\n        email: '<EMAIL>',\n        emailVerified: null,\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    mockDatabase.users.set(user.id, user);\n    // 创建示例邮箱\n    const email = {\n        id: 'email-1',\n        address: '<EMAIL>',\n        userId: user.id,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n    };\n    mockDatabase.emails.set(email.id, email);\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

});