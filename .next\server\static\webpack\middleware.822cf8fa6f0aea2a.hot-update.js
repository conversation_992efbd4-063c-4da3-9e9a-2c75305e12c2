"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDb: () => (/* binding */ createDb),\n/* harmony export */   mockDatabase: () => (/* binding */ mockDatabase)\n/* harmony export */ });\n// 使用单例模式确保数据持久化\nclass MockDatabase {\n    constructor(){\n        this.users = new Map();\n        this.emails = new Map();\n        this.messages = new Map();\n        this.emailCredentials = new Map();\n        this.roles = new Map();\n        this.userRoles = new Map();\n        this.apiKeys = new Map();\n        this.accounts = new Map();\n        this.sessions = new Map();\n        this.verificationTokens = new Map();\n        this.webhooks = new Map();\n    // 私有构造函数，确保单例\n    }\n    static getInstance() {\n        if (!MockDatabase.instance) {\n            MockDatabase.instance = new MockDatabase();\n        }\n        return MockDatabase.instance;\n    }\n}\n// 导出单例实例\nconst mockDatabase = MockDatabase.getInstance();\n// 初始化一些示例数据\nfunction initMockData() {\n    // 先从文件加载数据\n    loadFromFile();\n    // 检查是否已有角色数据\n    if (mockDatabase.roles.size > 0) return;\n    // 创建示例角色\n    const roles = [\n        {\n            id: 'emperor',\n            name: '皇帝',\n            description: '最高权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'duke',\n            name: '公爵',\n            description: '高级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'knight',\n            name: '骑士',\n            description: '中级权限',\n            createdAt: new Date()\n        },\n        {\n            id: 'civilian',\n            name: '平民',\n            description: '基础权限',\n            createdAt: new Date()\n        }\n    ];\n    roles.forEach((role)=>mockDatabase.roles.set(role.id, role));\n    saveToFile();\n    console.log('✅ 模拟数据库初始化完成');\n}\n// 初始化数据\ninitMockData();\nconst createDb = ()=>{\n    const mockDb = {\n        query: {\n            users: {\n                findFirst: (options)=>{\n                    const users = Array.from(mockDatabase.users.values());\n                    return Promise.resolve(users[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.users.values()))\n            },\n            emails: {\n                findFirst: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    return Promise.resolve(emails[0] || null);\n                },\n                findMany: (options)=>{\n                    const emails = Array.from(mockDatabase.emails.values());\n                    // 简单的过滤和排序\n                    return Promise.resolve(emails.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));\n                }\n            },\n            emailCredentials: {\n                findFirst: (options)=>{\n                    const credentials = Array.from(mockDatabase.emailCredentials.values());\n                    return Promise.resolve(credentials[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.emailCredentials.values()))\n            },\n            messages: {\n                findFirst: (options)=>{\n                    const messages = Array.from(mockDatabase.messages.values());\n                    return Promise.resolve(messages[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.messages.values()))\n            },\n            userRoles: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            roles: {\n                findFirst: (options)=>{\n                    const roles = Array.from(mockDatabase.roles.values());\n                    return Promise.resolve(roles[0] || null);\n                },\n                findMany: ()=>Promise.resolve(Array.from(mockDatabase.roles.values()))\n            },\n            apiKeys: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            accounts: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            sessions: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            verificationTokens: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            },\n            webhooks: {\n                findFirst: ()=>Promise.resolve(null),\n                findMany: ()=>Promise.resolve([])\n            }\n        },\n        select: (fields)=>({\n                from: (table)=>({\n                        where: (condition)=>{\n                            // 模拟 count 查询\n                            if (fields.count) {\n                                return Promise.resolve([\n                                    {\n                                        count: mockDatabase.emails.size\n                                    }\n                                ]);\n                            }\n                            return Promise.resolve([]);\n                        }\n                    })\n            }),\n        insert: (table)=>({\n                values: (data)=>{\n                    console.log('Mock insert:', data);\n                    // 模拟插入邮箱\n                    if (table === 'emails' || data.address && data.userId) {\n                        const emailId = `email-${Date.now()}`;\n                        const newEmail = {\n                            id: emailId,\n                            address: data.address,\n                            userId: data.userId,\n                            createdAt: new Date(),\n                            expiresAt: data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)\n                        };\n                        mockDatabase.emails.set(emailId, newEmail);\n                        return Promise.resolve({\n                            success: true,\n                            insertedId: emailId\n                        });\n                    }\n                    return Promise.resolve({\n                        success: true,\n                        insertedId: 'mock-id'\n                    });\n                }\n            }),\n        update: (table)=>({\n                set: (data)=>({\n                        where: (condition)=>{\n                            console.log('Mock update:', data);\n                            return Promise.resolve({\n                                success: true\n                            });\n                        }\n                    })\n            }),\n        delete: (table)=>({\n                where: (condition)=>{\n                    console.log('Mock delete');\n                    return Promise.resolve({\n                        success: true,\n                        changes: 0\n                    });\n                }\n            })\n    };\n    return mockDb;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/lib/db.ts\n");

/***/ })

});