"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/api/emails/[id]/route",{

/***/ "(rsc)/./app/api/emails/[id]/route.ts":
/*!**************************************!*\
  !*** ./app/api/emails/[id]/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _lib_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/schema */ \"(rsc)/./app/lib/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/sql.js\");\n/* harmony import */ var _lib_cursor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/cursor */ \"(rsc)/./app/lib/cursor.ts\");\n/* harmony import */ var _lib_apiKey__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/apiKey */ \"(rsc)/./app/lib/apiKey.ts\");\n\n\n\n\n\n\nconst runtime = \"edge\";\nasync function DELETE(request, { params }) {\n    const userId = await (0,_lib_apiKey__WEBPACK_IMPORTED_MODULE_4__.getUserId)();\n    try {\n        const db = (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.createDb)();\n        const { id } = await params;\n        const email = await db.query.emails.findFirst({\n            where: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails.userId, userId))\n        });\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"邮箱不存在或无权限删除\"\n            }, {\n                status: 403\n            });\n        }\n        await db.delete(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages.emailId, id));\n        await db.delete(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails.id, id));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Failed to delete email:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"删除邮箱失败\"\n        }, {\n            status: 500\n        });\n    }\n}\nconst PAGE_SIZE = 20;\nasync function GET(request, { params }) {\n    const { searchParams } = new URL(request.url);\n    const cursorStr = searchParams.get('cursor');\n    try {\n        const db = (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.createDb)();\n        const { id } = await params;\n        const userId = await (0,_lib_apiKey__WEBPACK_IMPORTED_MODULE_4__.getUserId)();\n        // 开发环境演示数据 - 与邮箱列表保持一致\n        const demoEmails = [\n            {\n                id: 'demo-email-1',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 60 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000) // 23小时后\n            },\n            {\n                id: 'demo-email-2',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 30 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000) // 23.5小时后\n            },\n            {\n                id: 'demo-email-3',\n                address: '<EMAIL>',\n                userId: userId,\n                createdAt: new Date(Date.now() - 10 * 60 * 1000),\n                expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000) // 23.8小时后\n            }\n        ];\n        // 先尝试从数据库查找\n        let email = await db.query.emails.findFirst({\n            where: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.emails.userId, userId))\n        });\n        // 如果数据库中没有，检查是否是演示邮箱\n        if (!email) {\n            email = demoEmails.find((e)=>e.id === id && e.userId === userId);\n        }\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"无权限查看\"\n            }, {\n                status: 403\n            });\n        }\n        const baseConditions = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages.emailId, id);\n        const totalResult = await db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.sql)`count(*)`\n        }).from(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages).where(baseConditions);\n        const totalCount = Number(totalResult[0].count);\n        const conditions = [\n            baseConditions\n        ];\n        if (cursorStr) {\n            const { timestamp, id } = (0,_lib_cursor__WEBPACK_IMPORTED_MODULE_3__.decodeCursor)(cursorStr);\n            conditions.push(// @ts-expect-error \"ignore the error\"\n            (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lt)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages.receivedAt, new Date(timestamp)), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages.receivedAt, new Date(timestamp)), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lt)(_lib_schema__WEBPACK_IMPORTED_MODULE_2__.messages.id, id))));\n        }\n        const results = await db.query.messages.findMany({\n            where: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)(...conditions),\n            orderBy: (messages, { desc })=>[\n                    desc(messages.receivedAt),\n                    desc(messages.id)\n                ],\n            limit: PAGE_SIZE + 1\n        });\n        const hasMore = results.length > PAGE_SIZE;\n        const nextCursor = hasMore ? (0,_lib_cursor__WEBPACK_IMPORTED_MODULE_3__.encodeCursor)(results[PAGE_SIZE - 1].receivedAt.getTime(), results[PAGE_SIZE - 1].id) : null;\n        const messageList = hasMore ? results.slice(0, PAGE_SIZE) : results;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            messages: messageList.map((msg)=>({\n                    id: msg.id,\n                    from_address: msg.fromAddress,\n                    subject: msg.subject,\n                    received_at: msg.receivedAt.getTime()\n                })),\n            nextCursor,\n            total: totalCount\n        });\n    } catch (error) {\n        console.error('Failed to fetch messages:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch messages\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/emails/[id]/route.ts\n");

/***/ })

});