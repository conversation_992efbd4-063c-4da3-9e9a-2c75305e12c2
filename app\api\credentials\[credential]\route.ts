import { NextResponse } from "next/server"
import { validateCredential } from "@/lib/emailCredentials"

export const runtime = "edge"

/**
 * 通过凭证获取邮箱信息
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ credential: string }> }
) {
  try {
    const { credential } = await params
    
    if (!credential) {
      return NextResponse.json(
        { error: "凭证不能为空" },
        { status: 400 }
      )
    }
    
    const credentialInfo = await validateCredential(credential)
    
    if (!credentialInfo) {
      return NextResponse.json(
        { error: "凭证无效或已过期" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      email: {
        id: credentialInfo.email.id,
        address: credentialInfo.email.address,
        createdAt: credentialInfo.email.createdAt.toISOString(),
        expiresAt: credentialInfo.email.expiresAt.toISOString()
      },
      credential: {
        id: credentialInfo.credential.id,
        expiresAt: credentialInfo.credential.expiresAt.toISOString(),
        accessCount: credentialInfo.credential.accessCount,
        lastAccessedAt: credentialInfo.credential.lastAccessedAt?.toISOString() || null
      }
    })
  } catch (error) {
    console.error('Failed to validate credential:', error)
    return NextResponse.json(
      { error: "验证凭证失败" },
      { status: 500 }
    )
  }
}
