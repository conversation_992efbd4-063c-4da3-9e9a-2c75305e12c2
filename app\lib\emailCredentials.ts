import { createDb } from "./db"
import { emailCredentials, emails } from "./schema"
import { eq, and, gt, sql } from "drizzle-orm"
import { nanoid } from "nanoid"

export interface EmailCredential {
  id: string
  emailId: string
  credential: string
  createdAt: Date
  expiresAt: Date
  lastAccessedAt: Date | null
  accessCount: number
  enabled: boolean
}

export interface CreateCredentialOptions {
  emailId: string
  expiryTime?: number // 毫秒，默认7天
}

export interface CredentialInfo {
  credential: EmailCredential
  email: {
    id: string
    address: string
    userId: string | null
    createdAt: Date
    expiresAt: Date
  }
}

/**
 * 生成邮箱凭证
 */
export async function generateEmailCredential(options: CreateCredentialOptions): Promise<string> {
  const db = createDb()

  // 检查邮箱是否存在（包括演示邮箱）
  let email = await db.query.emails.findFirst({
    where: eq(emails.id, options.emailId)
  })

  // 如果数据库中没有，检查是否是演示邮箱
  if (!email) {
    const demoEmails = [
      {
        id: 'demo-email-1',
        address: '<EMAIL>',
        userId: 'admin-user',
        createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1小时前
        expiresAt: new Date(Date.now() + 23 * 60 * 60 * 1000) // 23小时后
      },
      {
        id: 'demo-email-2',
        address: '<EMAIL>',
        userId: 'admin-user',
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
        expiresAt: new Date(Date.now() + 23.5 * 60 * 60 * 1000) // 23.5小时后
      },
      {
        id: 'demo-email-3',
        address: '<EMAIL>',
        userId: 'admin-user',
        createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10分钟前
        expiresAt: new Date(Date.now() + 23.8 * 60 * 60 * 1000) // 23.8小时后
      }
    ]

    email = demoEmails.find(e => e.id === options.emailId)
    console.log('🔍 查找演示邮箱:', options.emailId, '找到:', !!email)
  }

  if (!email) {
    throw new Error('邮箱不存在')
  }
  
  // 检查邮箱是否已过期
  if (email.expiresAt < new Date()) {
    throw new Error('邮箱已过期')
  }
  
  // 生成唯一凭证
  let credential: string
  let attempts = 0
  const maxAttempts = 10
  
  do {
    credential = nanoid(32) // 生成32位随机字符串
    const existing = await db.query.emailCredentials.findFirst({
      where: eq(emailCredentials.credential, credential)
    })
    
    if (!existing) break
    
    attempts++
    if (attempts >= maxAttempts) {
      throw new Error('生成凭证失败，请重试')
    }
  } while (true)
  
  // 设置凭证过期时间（默认7天）
  const expiryTime = options.expiryTime || (7 * 24 * 60 * 60 * 1000)
  const expiresAt = new Date(Date.now() + expiryTime)
  
  // 保存凭证
  await db.insert(emailCredentials).values({
    emailId: options.emailId,
    credential,
    expiresAt,
  })
  
  return credential
}

/**
 * 验证并获取凭证信息
 */
export async function validateCredential(credential: string): Promise<CredentialInfo | null> {
  const db = createDb()
  
  const credentialRecord = await db.query.emailCredentials.findFirst({
    where: and(
      eq(emailCredentials.credential, credential),
      eq(emailCredentials.enabled, true),
      gt(emailCredentials.expiresAt, new Date())
    ),
    with: {
      email: true
    }
  })
  
  if (!credentialRecord || !credentialRecord.email) {
    return null
  }
  
  // 检查邮箱是否仍然有效
  if (credentialRecord.email.expiresAt < new Date()) {
    return null
  }
  
  // 更新访问记录
  await db.update(emailCredentials)
    .set({
      lastAccessedAt: new Date(),
      accessCount: sql`${emailCredentials.accessCount} + 1`
    })
    .where(eq(emailCredentials.id, credentialRecord.id))
  
  return {
    credential: credentialRecord,
    email: credentialRecord.email
  }
}

/**
 * 获取邮箱的所有凭证
 */
export async function getEmailCredentials(emailId: string): Promise<EmailCredential[]> {
  const db = createDb()
  
  return await db.query.emailCredentials.findMany({
    where: eq(emailCredentials.emailId, emailId),
    orderBy: (emailCredentials, { desc }) => [desc(emailCredentials.createdAt)]
  })
}

/**
 * 禁用凭证
 */
export async function disableCredential(credentialId: string): Promise<boolean> {
  const db = createDb()
  
  const result = await db.update(emailCredentials)
    .set({ enabled: false })
    .where(eq(emailCredentials.id, credentialId))
  
  return result.success
}

/**
 * 删除过期凭证
 */
export async function cleanupExpiredCredentials(): Promise<number> {
  const db = createDb()
  
  const result = await db.delete(emailCredentials)
    .where(
      and(
        eq(emailCredentials.enabled, true),
        gt(new Date(), emailCredentials.expiresAt)
      )
    )
  
  return result.changes || 0
}

/**
 * 通过凭证获取邮箱访问权限（用于API）
 */
export async function getEmailByCredential(credential: string) {
  const credentialInfo = await validateCredential(credential)
  
  if (!credentialInfo) {
    return null
  }
  
  return {
    email: credentialInfo.email,
    credential: credentialInfo.credential
  }
}
